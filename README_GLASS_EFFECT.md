# 液态玻璃效果 Android 应用

这个Android应用实现了类似苹果系统的液态玻璃（Liquid Glass）效果，包含以下功能：

## 功能特性

### 1. 背景图片导入
- 点击"导入背景"按钮可以从设备相册选择图片作为背景
- 支持常见的图片格式（JPG, PNG等）
- 使用Coil库进行高效的图片加载和显示

### 2. 液态玻璃弹窗
- 点击"液态玻璃"按钮打开具有毛玻璃效果的弹窗
- 弹窗具有以下视觉特效：
  - 半透明背景
  - 渐变边框
  - 模糊效果
  - 高光反射
  - 多层次的透明度

## 技术实现

### 核心技术栈
- **Jetpack Compose**: 现代化的UI框架
- **Coil**: 图片加载库
- **Material3**: 设计系统

### 视觉效果实现
1. **模糊效果**: 使用`blur()`修饰符
2. **渐变背景**: 使用`Brush.linearGradient()`和`Brush.radialGradient()`
3. **透明度层叠**: 多层半透明颜色叠加
4. **边框高光**: 使用`border()`修饰符添加发光边框

### 文件结构
```
app/src/main/java/com/example/zuijiji2/
├── MainActivity.kt              # 主界面
├── GlassEffectComponents.kt     # 液态玻璃组件
└── ui/theme/                    # 主题配置
```

## 使用方法

1. **导入背景图片**:
   - 点击屏幕底部的"导入背景"按钮
   - 从相册中选择一张图片
   - 图片将自动设置为应用背景

2. **查看液态玻璃效果**:
   - 点击"液态玻璃"按钮
   - 观察弹窗的毛玻璃效果
   - 点击弹窗外部或"取消"/"确定"按钮关闭

## 自定义选项

### 调整玻璃效果
在`GlassEffectComponents.kt`中可以调整：
- 透明度值（alpha参数）
- 模糊半径（blur radius）
- 渐变颜色
- 边框样式

### 修改按钮样式
在`MainActivity.kt`中可以自定义：
- 按钮颜色和透明度
- 圆角半径
- 边框效果
- 文字样式

## 权限要求

应用需要以下权限来访问设备存储：
- `READ_EXTERNAL_STORAGE`
- `READ_MEDIA_IMAGES` (Android 13+)

这些权限已在`AndroidManifest.xml`中声明。

## 兼容性

- **最低SDK版本**: 24 (Android 7.0)
- **目标SDK版本**: 35 (Android 15)
- **推荐设备**: 支持硬件加速的Android设备

## 性能优化

1. **图片加载**: 使用Coil的内存缓存和磁盘缓存
2. **渲染优化**: 合理使用Compose的重组机制
3. **内存管理**: 及时释放不需要的图片资源

## 扩展建议

1. **添加更多玻璃效果样式**
2. **支持动态背景（视频、动画）**
3. **添加手势交互（拖拽、缩放）**
4. **实现实时背景模糊**
5. **添加音效和触觉反馈**
