  Activity android.app  
MainScreen android.app.Activity  
Zuijiji2Theme android.app.Activity  enableEdgeToEdge android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Context android.content  
MainScreen android.content.Context  
Zuijiji2Theme android.content.Context  enableEdgeToEdge android.content.Context  
setContent android.content.Context  
MainScreen android.content.ContextWrapper  
Zuijiji2Theme android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  
setContent android.content.ContextWrapper  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
MainScreen  android.view.ContextThemeWrapper  
Zuijiji2Theme  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
Zuijiji2Theme #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
MainScreen -androidx.activity.ComponentActivity.Companion  
Zuijiji2Theme -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  detectDragGestures $androidx.compose.foundation.gestures  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Canvas "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  
MagnifierView "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  
Zuijiji2Theme "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberAsyncImagePainter "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  with "androidx.compose.foundation.layout  Add +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Canvas +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  FloatingActionButton +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  
MagnifierView +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  Search +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  rememberAsyncImagePainter +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  with +androidx.compose.foundation.layout.BoxScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Box androidx.compose.material3  Bundle androidx.compose.material3  Canvas androidx.compose.material3  CircleShape androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  Icons androidx.compose.material3  Image androidx.compose.material3  
MagnifierView androidx.compose.material3  
MainScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Offset androidx.compose.material3  Preview androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  
Zuijiji2Theme androidx.compose.material3  align androidx.compose.material3  androidx androidx.compose.material3  
background androidx.compose.material3  clip androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  getValue androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  offset androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberAsyncImagePainter androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  with androidx.compose.material3  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  Canvas androidx.compose.runtime  CircleShape androidx.compose.runtime  Color androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  Icon androidx.compose.runtime  Icons androidx.compose.runtime  Image androidx.compose.runtime  
MagnifierView androidx.compose.runtime  
MainScreen androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  Preview androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  
Zuijiji2Theme androidx.compose.runtime  align androidx.compose.runtime  androidx androidx.compose.runtime  
background androidx.compose.runtime  clip androidx.compose.runtime  fillMaxSize androidx.compose.runtime  getValue androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  offset androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberAsyncImagePainter androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  with androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	BottomEnd androidx.compose.ui.Alignment  BottomStart androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  BottomStart 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  plus #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  ActivityResultContracts androidx.compose.ui.graphics  	Alignment androidx.compose.ui.graphics  Box androidx.compose.ui.graphics  Bundle androidx.compose.ui.graphics  Canvas androidx.compose.ui.graphics  CircleShape androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  ComponentActivity androidx.compose.ui.graphics  
Composable androidx.compose.ui.graphics  ContentScale androidx.compose.ui.graphics  FloatingActionButton androidx.compose.ui.graphics  Icon androidx.compose.ui.graphics  Icons androidx.compose.ui.graphics  Image androidx.compose.ui.graphics  
MagnifierView androidx.compose.ui.graphics  
MainScreen androidx.compose.ui.graphics  Modifier androidx.compose.ui.graphics  Offset androidx.compose.ui.graphics  Preview androidx.compose.ui.graphics  Unit androidx.compose.ui.graphics  Uri androidx.compose.ui.graphics  
Zuijiji2Theme androidx.compose.ui.graphics  align androidx.compose.ui.graphics  androidx androidx.compose.ui.graphics  
background androidx.compose.ui.graphics  clip androidx.compose.ui.graphics  fillMaxSize androidx.compose.ui.graphics  getValue androidx.compose.ui.graphics  let androidx.compose.ui.graphics  mutableStateOf androidx.compose.ui.graphics  offset androidx.compose.ui.graphics  padding androidx.compose.ui.graphics  provideDelegate androidx.compose.ui.graphics  remember androidx.compose.ui.graphics  rememberAsyncImagePainter androidx.compose.ui.graphics  setValue androidx.compose.ui.graphics  size androidx.compose.ui.graphics  with androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  clipPath &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  detectDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  toDp  androidx.compose.ui.unit.Density  toPx  androidx.compose.ui.unit.Density  times androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  
unaryMinus androidx.compose.ui.unit.Dp  toDp $androidx.compose.ui.unit.FontScaling  Bundle #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
Zuijiji2Theme #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  AsyncImagePainter coil.compose  rememberAsyncImagePainter coil.compose  ActivityResultContracts com.example.zuijiji2  	Alignment com.example.zuijiji2  Box com.example.zuijiji2  Bundle com.example.zuijiji2  Canvas com.example.zuijiji2  CircleShape com.example.zuijiji2  Color com.example.zuijiji2  ComponentActivity com.example.zuijiji2  
Composable com.example.zuijiji2  ContentScale com.example.zuijiji2  FloatingActionButton com.example.zuijiji2  Icon com.example.zuijiji2  Icons com.example.zuijiji2  Image com.example.zuijiji2  
MagnifierView com.example.zuijiji2  MainActivity com.example.zuijiji2  
MainScreen com.example.zuijiji2  MainScreenPreview com.example.zuijiji2  Modifier com.example.zuijiji2  Offset com.example.zuijiji2  Preview com.example.zuijiji2  Unit com.example.zuijiji2  Uri com.example.zuijiji2  
Zuijiji2Theme com.example.zuijiji2  align com.example.zuijiji2  androidx com.example.zuijiji2  
background com.example.zuijiji2  clip com.example.zuijiji2  fillMaxSize com.example.zuijiji2  getValue com.example.zuijiji2  let com.example.zuijiji2  mutableStateOf com.example.zuijiji2  offset com.example.zuijiji2  padding com.example.zuijiji2  provideDelegate com.example.zuijiji2  remember com.example.zuijiji2  rememberAsyncImagePainter com.example.zuijiji2  setValue com.example.zuijiji2  size com.example.zuijiji2  with com.example.zuijiji2  
MainScreen !com.example.zuijiji2.MainActivity  
Zuijiji2Theme !com.example.zuijiji2.MainActivity  enableEdgeToEdge !com.example.zuijiji2.MainActivity  
setContent !com.example.zuijiji2.MainActivity  Boolean com.example.zuijiji2.ui.theme  Build com.example.zuijiji2.ui.theme  
Composable com.example.zuijiji2.ui.theme  DarkColorScheme com.example.zuijiji2.ui.theme  
FontFamily com.example.zuijiji2.ui.theme  
FontWeight com.example.zuijiji2.ui.theme  LightColorScheme com.example.zuijiji2.ui.theme  Pink40 com.example.zuijiji2.ui.theme  Pink80 com.example.zuijiji2.ui.theme  Purple40 com.example.zuijiji2.ui.theme  Purple80 com.example.zuijiji2.ui.theme  PurpleGrey40 com.example.zuijiji2.ui.theme  PurpleGrey80 com.example.zuijiji2.ui.theme  
Typography com.example.zuijiji2.ui.theme  Unit com.example.zuijiji2.ui.theme  
Zuijiji2Theme com.example.zuijiji2.ui.theme  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  let kotlin  with kotlin  not kotlin.Boolean  dp 
kotlin.Double  sp 
kotlin.Double  div kotlin.Float  minus kotlin.Float  times kotlin.Float  toDp kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  SuspendFunction1 kotlin.coroutines  sqrt kotlin.math  KMutableProperty0 kotlin.reflect  offset &androidx.compose.ui.Modifier.Companion  consume 4androidx.compose.ui.input.pointer.PointerInputChange  ActivityResultContracts androidx.compose.animation.core  	Alignment androidx.compose.animation.core  Box androidx.compose.animation.core  Brush androidx.compose.animation.core  Bundle androidx.compose.animation.core  Canvas androidx.compose.animation.core  CircleShape androidx.compose.animation.core  Color androidx.compose.animation.core  ComponentActivity androidx.compose.animation.core  
Composable androidx.compose.animation.core  ContentScale androidx.compose.animation.core  Easing androidx.compose.animation.core  FastOutSlowInEasing androidx.compose.animation.core  FloatingActionButton androidx.compose.animation.core  Icon androidx.compose.animation.core  Icons androidx.compose.animation.core  Image androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  LinearEasing androidx.compose.animation.core  LiquidGlassButton androidx.compose.animation.core  LiquidGlassEffect androidx.compose.animation.core  LiquidGlassMagnifierView androidx.compose.animation.core  
MainScreen androidx.compose.animation.core  Modifier androidx.compose.animation.core  Offset androidx.compose.animation.core  PI androidx.compose.animation.core  Preview androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  String androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  Unit androidx.compose.animation.core  Uri androidx.compose.animation.core  
Zuijiji2Theme androidx.compose.animation.core  align androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloat androidx.compose.animation.core  
background androidx.compose.animation.core  blur androidx.compose.animation.core  clip androidx.compose.animation.core  
coerceAtLeast androidx.compose.animation.core  cos androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  getValue androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  let androidx.compose.animation.core  linearGradient androidx.compose.animation.core  listOf androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  offset androidx.compose.animation.core  padding androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  radialGradient androidx.compose.animation.core  remember androidx.compose.animation.core  rememberAsyncImagePainter androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  setValue androidx.compose.animation.core  sin androidx.compose.animation.core  size androidx.compose.animation.core  tween androidx.compose.animation.core  with androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  Restart *androidx.compose.animation.core.RepeatMode  Reverse *androidx.compose.animation.core.RepeatMode  compose (androidx.compose.animation.core.androidx  ui 0androidx.compose.animation.core.androidx.compose  graphics 3androidx.compose.animation.core.androidx.compose.ui  vector <androidx.compose.animation.core.androidx.compose.ui.graphics  ImageVector Candroidx.compose.animation.core.androidx.compose.ui.graphics.vector  Brush "androidx.compose.foundation.layout  FastOutSlowInEasing "androidx.compose.foundation.layout  LinearEasing "androidx.compose.foundation.layout  LiquidGlassButton "androidx.compose.foundation.layout  LiquidGlassEffect "androidx.compose.foundation.layout  LiquidGlassMagnifierView "androidx.compose.foundation.layout  PI "androidx.compose.foundation.layout  
RepeatMode "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  animateFloat "androidx.compose.foundation.layout  blur "androidx.compose.foundation.layout  
coerceAtLeast "androidx.compose.foundation.layout  cos "androidx.compose.foundation.layout  infiniteRepeatable "androidx.compose.foundation.layout  linearGradient "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  radialGradient "androidx.compose.foundation.layout  rememberInfiniteTransition "androidx.compose.foundation.layout  sin "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  Brush +androidx.compose.foundation.layout.BoxScope  LiquidGlassButton +androidx.compose.foundation.layout.BoxScope  LiquidGlassEffect +androidx.compose.foundation.layout.BoxScope  LiquidGlassMagnifierView +androidx.compose.foundation.layout.BoxScope  PI +androidx.compose.foundation.layout.BoxScope  blur +androidx.compose.foundation.layout.BoxScope  cos +androidx.compose.foundation.layout.BoxScope  linearGradient +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  radialGradient +androidx.compose.foundation.layout.BoxScope  sin +androidx.compose.foundation.layout.BoxScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  vector ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  ImageVector Fandroidx.compose.foundation.layout.androidx.compose.ui.graphics.vector  Brush androidx.compose.material3  FastOutSlowInEasing androidx.compose.material3  LinearEasing androidx.compose.material3  LiquidGlassButton androidx.compose.material3  LiquidGlassEffect androidx.compose.material3  LiquidGlassMagnifierView androidx.compose.material3  PI androidx.compose.material3  
RepeatMode androidx.compose.material3  String androidx.compose.material3  animateFloat androidx.compose.material3  blur androidx.compose.material3  
coerceAtLeast androidx.compose.material3  cos androidx.compose.material3  infiniteRepeatable androidx.compose.material3  linearGradient androidx.compose.material3  listOf androidx.compose.material3  radialGradient androidx.compose.material3  rememberInfiniteTransition androidx.compose.material3  sin androidx.compose.material3  tween androidx.compose.material3  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  vector 7androidx.compose.material3.androidx.compose.ui.graphics  ImageVector >androidx.compose.material3.androidx.compose.ui.graphics.vector  Brush androidx.compose.runtime  FastOutSlowInEasing androidx.compose.runtime  LinearEasing androidx.compose.runtime  LiquidGlassButton androidx.compose.runtime  LiquidGlassEffect androidx.compose.runtime  LiquidGlassMagnifierView androidx.compose.runtime  PI androidx.compose.runtime  
RepeatMode androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  animateFloat androidx.compose.runtime  blur androidx.compose.runtime  
coerceAtLeast androidx.compose.runtime  cos androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  linearGradient androidx.compose.runtime  listOf androidx.compose.runtime  radialGradient androidx.compose.runtime  rememberInfiniteTransition androidx.compose.runtime  sin androidx.compose.runtime  tween androidx.compose.runtime  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  vector 5androidx.compose.runtime.androidx.compose.ui.graphics  ImageVector <androidx.compose.runtime.androidx.compose.ui.graphics.vector  blur androidx.compose.ui.Modifier  drawWithContent androidx.compose.ui.Modifier  blur androidx.compose.ui.draw  drawWithContent androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  FastOutSlowInEasing androidx.compose.ui.graphics  LinearEasing androidx.compose.ui.graphics  LiquidGlassButton androidx.compose.ui.graphics  LiquidGlassEffect androidx.compose.ui.graphics  LiquidGlassMagnifierView androidx.compose.ui.graphics  PI androidx.compose.ui.graphics  
RepeatMode androidx.compose.ui.graphics  String androidx.compose.ui.graphics  animateFloat androidx.compose.ui.graphics  blur androidx.compose.ui.graphics  
coerceAtLeast androidx.compose.ui.graphics  cos androidx.compose.ui.graphics  infiniteRepeatable androidx.compose.ui.graphics  linearGradient androidx.compose.ui.graphics  listOf androidx.compose.ui.graphics  radialGradient androidx.compose.ui.graphics  rememberInfiniteTransition androidx.compose.ui.graphics  sin androidx.compose.ui.graphics  tween androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  linearGradient "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  linearGradient ,androidx.compose.ui.graphics.Brush.Companion  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  Blue "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  compose %androidx.compose.ui.graphics.androidx  ui -androidx.compose.ui.graphics.androidx.compose  graphics 0androidx.compose.ui.graphics.androidx.compose.ui  vector 9androidx.compose.ui.graphics.androidx.compose.ui.graphics  ImageVector @androidx.compose.ui.graphics.androidx.compose.ui.graphics.vector  ContentDrawScope &androidx.compose.ui.graphics.drawscope  Brush 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  Color 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  center 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  
drawCircle 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  drawContent 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  listOf 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  radialGradient 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  size 7androidx.compose.ui.graphics.drawscope.ContentDrawScope  Brush 0androidx.compose.ui.graphics.drawscope.DrawScope  PI 0androidx.compose.ui.graphics.drawscope.DrawScope  center 0androidx.compose.ui.graphics.drawscope.DrawScope  
coerceAtLeast 0androidx.compose.ui.graphics.drawscope.DrawScope  cos 0androidx.compose.ui.graphics.drawscope.DrawScope  drawRect 0androidx.compose.ui.graphics.drawscope.DrawScope  linearGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  radialGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  sin 0androidx.compose.ui.graphics.drawscope.DrawScope  Brush com.example.zuijiji2  FastOutSlowInEasing com.example.zuijiji2  LinearEasing com.example.zuijiji2  LiquidGlassButton com.example.zuijiji2  LiquidGlassEffect com.example.zuijiji2  LiquidGlassMagnifierView com.example.zuijiji2  PI com.example.zuijiji2  
RepeatMode com.example.zuijiji2  String com.example.zuijiji2  animateFloat com.example.zuijiji2  blur com.example.zuijiji2  
coerceAtLeast com.example.zuijiji2  cos com.example.zuijiji2  infiniteRepeatable com.example.zuijiji2  linearGradient com.example.zuijiji2  listOf com.example.zuijiji2  radialGradient com.example.zuijiji2  rememberInfiniteTransition com.example.zuijiji2  sin com.example.zuijiji2  tween com.example.zuijiji2  compose com.example.zuijiji2.androidx  ui %com.example.zuijiji2.androidx.compose  graphics (com.example.zuijiji2.androidx.compose.ui  vector 1com.example.zuijiji2.androidx.compose.ui.graphics  ImageVector 8com.example.zuijiji2.androidx.compose.ui.graphics.vector  div 
kotlin.Double  times 
kotlin.Double  toFloat 
kotlin.Double  
coerceAtLeast kotlin.Float  plus kotlin.Float  
unaryMinus kotlin.Float  rangeTo 
kotlin.Int  times 
kotlin.Int  IntIterator kotlin.collections  List kotlin.collections  listOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  PI kotlin.math  cos kotlin.math  sin kotlin.math  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  
coerceAtLeast 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  
KProperty0 kotlin.reflect  Greeting android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  fillMaxSize android.app.Activity  padding android.app.Activity  Greeting android.content.Context  Modifier android.content.Context  Scaffold android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  Greeting android.content.ContextWrapper  Modifier android.content.ContextWrapper  Scaffold android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  Greeting  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  Greeting #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  Greeting -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  
PaddingValues "androidx.compose.foundation.layout  Scaffold androidx.compose.material3  Text androidx.compose.material3  padding &androidx.compose.ui.Modifier.Companion  Greeting #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  Greeting com.example.zuijiji2  GreetingPreview com.example.zuijiji2  Scaffold com.example.zuijiji2  Greeting !com.example.zuijiji2.MainActivity  Modifier !com.example.zuijiji2.MainActivity  Scaffold !com.example.zuijiji2.MainActivity  fillMaxSize !com.example.zuijiji2.MainActivity  padding !com.example.zuijiji2.MainActivity  BorderStroke androidx.compose.foundation  border androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  EnhancedGlassDialog "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  GlassButton "androidx.compose.foundation.layout  ImageRequest "androidx.compose.foundation.layout  LocalContext "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  border "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  run "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Arrangement +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  EnhancedGlassDialog +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  GlassButton +androidx.compose.foundation.layout.BoxScope  ImageRequest +androidx.compose.foundation.layout.BoxScope  LocalContext +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  border +androidx.compose.foundation.layout.BoxScope  buttonColors +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  run +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  GlassButton .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  Brush +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  GlassButton +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  linearGradient +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Column androidx.compose.material3  EnhancedGlassDialog androidx.compose.material3  
FontWeight androidx.compose.material3  GlassButton androidx.compose.material3  ImageRequest androidx.compose.material3  LocalContext androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Spacer androidx.compose.material3  border androidx.compose.material3  buttonColors androidx.compose.material3  fillMaxWidth androidx.compose.material3  height androidx.compose.material3  run androidx.compose.material3  spacedBy androidx.compose.material3  verticalGradient androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Column androidx.compose.runtime  EnhancedGlassDialog androidx.compose.runtime  
FontWeight androidx.compose.runtime  GlassButton androidx.compose.runtime  ImageRequest androidx.compose.runtime  LocalContext androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Spacer androidx.compose.runtime  Text androidx.compose.runtime  border androidx.compose.runtime  buttonColors androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  height androidx.compose.runtime  run androidx.compose.runtime  spacedBy androidx.compose.runtime  verticalGradient androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  BottomCenter androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  border androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  height &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  ImageRequest coil.request  Builder coil.request.ImageRequest  build !coil.request.ImageRequest.Builder  data !coil.request.ImageRequest.Builder  Arrangement com.example.zuijiji2  Boolean com.example.zuijiji2  Button com.example.zuijiji2  ButtonDefaults com.example.zuijiji2  Column com.example.zuijiji2  EnhancedGlassDialog com.example.zuijiji2  
FontWeight com.example.zuijiji2  GlassButton com.example.zuijiji2  ImageRequest com.example.zuijiji2  LocalContext com.example.zuijiji2  RoundedCornerShape com.example.zuijiji2  Row com.example.zuijiji2  Spacer com.example.zuijiji2  Text com.example.zuijiji2  border com.example.zuijiji2  buttonColors com.example.zuijiji2  fillMaxWidth com.example.zuijiji2  height com.example.zuijiji2  run com.example.zuijiji2  spacedBy com.example.zuijiji2  verticalGradient com.example.zuijiji2  weight com.example.zuijiji2  width com.example.zuijiji2  run kotlin                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     